<template>
	<view class="report-edit-page">
		<!-- 表单内容 -->
		<scroll-view scroll-y="true" class="form-container">
			<view class="form-card">
				<!-- 生产编号 -->
				<view class="form-item">
					<text class="form-label required">生产编号</text>
					<input
						class="form-input disabled"
						:value="formData.workNo"
						placeholder="自动填充"
						disabled
					/>
				</view>

				<!-- 任务类型 -->
				<view class="form-item">
					<text class="form-label required">任务类型</text>
					<SelectPicker
						v-model="formData.type"
						:options="workTypeOptions || []"
						placeholder="请选择任务类型"
						labelField="label"
						valueField="value"
						title="选择任务类型"
						@change="handleTypeChange"
					/>
				</view>

				<!-- 报工编号 -->
				<view class="form-item">
					<text class="form-label">报工编号</text>
					<input
						class="form-input disabled"
						:value="formData.reportCode"
						placeholder="保存自动生成"
						disabled
					/>
				</view>
				<!-- 开始时间 -->
				<view class="form-item">
					<text class="form-label required">开始时间</text>
					<uni-datetime-picker
						type="datetime"
						:value="startTimeValue"
						@change="handleStartTimeChange"
						placeholder="请选择开始时间"
						:clear-icon="false"
					/>
				</view>

				<!-- 结束时间 -->
				<view class="form-item">
					<text class="form-label required">结束时间</text>
					<uni-datetime-picker
						type="datetime"
						:value="endTimeValue"
						@change="handleEndTimeChange"
						placeholder="请选择结束时间"
						:clear-icon="false"
					/>
				</view>

			<!-- 用时 -->
			<view class="form-item">
				<text class="form-label">用时</text>
				<view class="duration-input-group">
					<input
						class="duration-input"
						type="number"
						v-model="durationHoursDisplay"
						placeholder="0"
						@input="handleHoursInput"
						@blur="updateCostTime"
					/>
					<text class="duration-label">小时</text>
					<input
						class="duration-input"
						type="number"
						v-model="durationMinutesDisplay"
						placeholder="0"
						@input="handleMinutesInput"
						@blur="updateCostTime"
					/>
					<text class="duration-label">分钟</text>
				</view>
			</view>
			<!-- 人数 -->
			<view class="form-item">
				<text class="form-label">人数</text>
				<input
					class="form-input"
					type="number"
					:value="formData.costHeadcount || ''"
					@input="handleHeadcountInput"
					placeholder="请输入人数"
				/>
			</view>

			<!-- 生产线 -->
			<view class="form-item">
				<text class="form-label">生产线</text>
				<SelectPicker
					v-model="formData.line"
					:options="lineOptions || []"
					placeholder="请选择生产线"
					labelField="label"
					valueField="value"
					title="选择生产线"
					@change="handleLineChange"
				/>
			</view>

			<!-- 数量 -->
			<view class="form-item">
				<text class="form-label required">数量</text>
				<view class="quantity-input-group">
					<input
						class="form-input"
						type="digit"
						:value="formData.quantity || ''"
						@input="handleQuantityInput"
						placeholder="请输入数量"
					/>
					<text class="unit-text">{{ productUnitName }}</text>
				</view>
			</view>

			<!-- 件数 -->
			<view class="form-item">
				<text class="form-label">件数</text>
				<input
					class="form-input"
					type="number"
					:value="formData.piece || ''"
					@input="handlePieceInput"
					placeholder="请输入件数"
				/>
			</view>
			<!-- 批号 -->
			<view class="form-item">
				<text class="form-label">批号</text>
				<input
					class="form-input"
					v-model="formData.batchNo"
					placeholder="请输入批号"
				/>
			</view>

			<!-- 温度 -->
			<view class="form-item">
				<text class="form-label">温度</text>
				<input
					class="form-input"
					type="digit"
					:value="formData.temperature || ''"
					@input="handleTemperatureInput"
					placeholder="请输入温度"
				/>
			</view>

			<!-- 湿度 -->
			<view class="form-item">
				<text class="form-label">湿度</text>
				<input
					class="form-input"
					type="digit"
					:value="formData.humidity || ''"
					@input="handleHumidityInput"
					placeholder="请输入湿度"
				/>
			</view>

			<!-- 备注 -->
			<view class="form-item">
				<text class="form-label">备注</text>
				<textarea
					class="form-textarea"
					v-model="formData.remark"
					placeholder="请输入备注信息"
					:maxlength="200"
					show-count
				></textarea>
			</view>
		</view>
	</scroll-view>

	<!-- 底部固定按钮 -->
	<view class="bottom-actions">
		<button class="btn-cancel" @click="goBack">取消</button>
		<button class="btn-submit" @click="submitForm" :disabled="formLoading">
			{{ formLoading ? '保存中...' : '保存' }}
		</button>
	</view>

		<!-- 加载遮罩 -->
		<view v-if="formLoading" class="loading-mask">
			<view class="loading-content">
				<uni-icons type="spinner-cycle" size="30" color="#007AFF"></uni-icons>
				<text class="loading-text">保存中...</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getWorkOrderApi } from '../../../../../api/scm/mfg/workorder'
import { getReportOrderApi, createReportOrderApi, updateReportOrderApi } from '../../../../../api/scm/mfg/reportorder'
import { getTempHumiditySimpleInfoApi } from '../../../../../api/scm/iot/device/temphumidity'
import { getUnitApi } from '../../../../../api/scm/base/unit'
import { getDictOptions, DICT_TYPE } from '../../../../../utils/dict'
import SelectPicker from '../../../../../components/SelectPicker/SelectPicker.vue'

export default {
	components: {
		SelectPicker
	},
	data() {
		return {
			// 表单数据
			formData: {
				id: null,
				workId: '',
				workNo: '',
				type: '',
				reportCode: '',
				startTime: null,
				endTime: null,
				costTime: 0,
				costHeadcount: '',
				line: '',
				quantity: '',
				piece: '',
				batchNo: '',
				temperature: '',
				humidity: '',
				remark: ''
			},
			
			// 字典选项
			workTypeOptions: [],
			lineOptions: [],
			
			// 用时输入相关变量
			durationHoursDisplay: '0',
			durationMinutesDisplay: '0',
			
			// 产品单位名称
			productUnitName: '',
			
			// 加载状态
			formLoading: false,
			
			// 工单数据
			workOrderData: null
		}
	},

	computed: {
		// 将开始时间时间戳转换为字符串供 uni-datetime-picker 使用
		startTimeValue() {
			if (this.formData.startTime) {
				return this.formatDateTime(new Date(this.formData.startTime))
			}
			return ''
		},

		// 将结束时间时间戳转换为字符串供 uni-datetime-picker 使用
		endTimeValue() {
			if (this.formData.endTime) {
				return this.formatDateTime(new Date(this.formData.endTime))
			}
			return ''
		}
	},

	async onLoad() {
		// 获取页面参数
		const eventChannel = this.getOpenerEventChannel()
		if (eventChannel) {
			eventChannel.on('acceptDataFormOpener', (data) => {
				console.log('接收到的数据:', data)
				if (data.workId) {
					this.formData.workId = data.workId
					this.initReportOrderData()
				}
			})
		} else {
			uni.showToast({
				title: '页面访问异常',
				icon: 'error'
			})
		}
		
		// 初始化字典数据
		await this.initDictData()

		// 确保表单数据有效
		this.ensureValidFormData()
	},
	
	methods: {
		// 初始化报工数据
		async initReportOrderData() {
			if (this.formData.workId) {
				try {
					const workOrderData = await getWorkOrderApi(this.formData.workId)
					this.workOrderData = workOrderData.data
					
					// 自动填充工单相关字段
					this.formData.workNo = workOrderData.data.workNo || ''
					this.formData.line = workOrderData.data.scheduleLine || workOrderData.data.actualLine || ''
					this.formData.quantity = workOrderData.data.scheduleQuantity || ''
					this.formData.piece = workOrderData.data.schedulePiece || ''
					this.formData.costHeadcount = workOrderData.data.scheduleHeadcount || ''
					this.formData.batchNo = this.formatDate(new Date(), 'yyyyMMdd') // 批号为当前日期
					this.formData.type = '2' // 生产
					
					// 填充开始时间
					if (workOrderData.data.scheduleStartTime) {
						this.formData.startTime = new Date(workOrderData.data.scheduleStartTime).getTime()
					} else if (workOrderData.data.actualStartTime) {
						this.formData.startTime = new Date(workOrderData.data.actualStartTime).getTime()
					}

					// 填充结束时间
					if (workOrderData.data.scheduleEndTime) {
						this.formData.endTime = new Date(workOrderData.data.scheduleEndTime).getTime()
					} else if (workOrderData.data.actualEndTime) {
						this.formData.endTime = new Date(workOrderData.data.actualEndTime).getTime()
					}
					
					// 计算用时
					this.calculateCostTime()

					// 确保表单数据有效
					this.ensureValidFormData()
					
					// 设置产品单位名称
					if (workOrderData.data.productUnit) {
						this.productUnitName = await this.getUnitName(workOrderData.data.productUnit)
					}
					
				} catch (error) {
					console.error('获取工单信息失败:', error)
					uni.showToast({
						title: '获取工单信息失败',
						icon: 'error'
					})
				}
			}
		},
		
		// 初始化字典数据
		async initDictData() {
			try {
				// 获取任务类型选项
				this.workTypeOptions = await getDictOptions(DICT_TYPE.MFG_WORK_TYPE)
				
				// 获取生产线选项
				this.lineOptions = await getDictOptions(DICT_TYPE.MANUFACTURE_LINE)
				
			} catch (error) {
				console.error('获取字典数据失败:', error)
			}
		},
		

		
		// 处理开始时间变化
		handleStartTimeChange(e) {
			if (e) {
				// 将时间字符串转换为时间戳
				const timestamp = new Date(e).getTime()
				this.formData.startTime = timestamp
			} else {
				this.formData.startTime = null
			}
			this.calculateCostTime()
		},

		// 处理结束时间变化
		handleEndTimeChange(e) {
			if (e) {
				// 将时间字符串转换为时间戳
				const timestamp = new Date(e).getTime()
				this.formData.endTime = timestamp
			} else {
				this.formData.endTime = null
			}
			this.calculateCostTime()
		},
		
		// 处理任务类型变化
		handleTypeChange(e) {
			this.formData.type = e.value
		},
		
		// 处理生产线变化
		handleLineChange(e) {
			this.formData.line = e.value
			// 如果生产线已选择且时间已填写，则自动获取温湿度
			if (this.formData.line && this.formData.startTime && this.formData.endTime) {
				this.fetchTempHumidityData()
			}
		},
		
		// 处理小时输入
		handleHoursInput(e) {
			const value = e.detail.value.replace(/[^\d]/g, '')
			this.durationHoursDisplay = value
		},
		
		// 处理分钟输入
		handleMinutesInput(e) {
			let value = e.detail.value.replace(/[^\d]/g, '')
			if (parseInt(value) > 59) {
				value = '59'
			}
			this.durationMinutesDisplay = value
		},
		
		// 处理人数输入
		handleHeadcountInput(e) {
			const value = e.detail.value.replace(/[^\d]/g, '')
			this.formData.costHeadcount = value ? parseInt(value) : ''
		},
		
		// 处理数量输入
		handleQuantityInput(e) {
			const value = e.detail.value.replace(/[^\d.]/g, '')
			this.formData.quantity = value ? parseFloat(value) : ''
		},
		
		// 处理件数输入
		handlePieceInput(e) {
			const value = e.detail.value.replace(/[^\d]/g, '')
			this.formData.piece = value ? parseInt(value) : ''
		},
		
		// 处理温度输入
		handleTemperatureInput(e) {
			const value = e.detail.value.replace(/[^\d.-]/g, '')
			this.formData.temperature = value ? parseFloat(value) : ''
		},
		
		// 处理湿度输入
		handleHumidityInput(e) {
			const value = e.detail.value.replace(/[^\d.-]/g, '')
			this.formData.humidity = value ? parseFloat(value) : ''
		},
		
		// 手动更新用时
		updateCostTime() {
			const hours = parseInt(this.durationHoursDisplay) || 0
			const minutes = parseInt(this.durationMinutesDisplay) || 0
			const totalMinutes = hours * 60 + minutes
			this.formData.costTime = totalMinutes
		},
		
		// 从用时分钟数更新小时分钟输入框
		updateDurationInputs(totalMinutes) {
			const hours = Math.floor(totalMinutes / 60)
			const minutes = totalMinutes % 60
			this.durationHoursDisplay = hours.toString()
			this.durationMinutesDisplay = minutes.toString()
		},
		
		// 计算用时
		calculateCostTime() {
			if (this.formData.startTime && this.formData.endTime) {
				const startTime = new Date(this.formData.startTime)
				const endTime = new Date(this.formData.endTime)
				
				// 计算时间差（毫秒）
				const timeDiff = endTime.getTime() - startTime.getTime()
				
				if (timeDiff > 0) {
					// 转换为分钟，精确到分钟
					const minutes = Math.round(timeDiff / (1000 * 60))
					
					// 更新用时字段（存储分钟数）
					this.formData.costTime = minutes
					
					// 更新小时分钟输入框
					this.updateDurationInputs(minutes)
				} else {
					this.formData.costTime = 0
					this.durationHoursDisplay = '0'
					this.durationMinutesDisplay = '0'
				}
			} else {
				this.formData.costTime = 0
				this.durationHoursDisplay = '0'
				this.durationMinutesDisplay = '0'
			}
		},
		
		// 获取温湿度数据
		async fetchTempHumidityData() {
			try {
				// 检查必要参数是否存在
				if (!this.formData.line || !this.formData.startTime || !this.formData.endTime) {
					return
				}

				const params = {
					line: this.formData.line,
					startTime: this.formatDateTime(this.formData.startTime),
					endTime: this.formatDateTime(this.formData.endTime)
				}

				const res = await getTempHumiditySimpleInfoApi(params)
				if (res && res.data) {
					// 填充温湿度数据到表单
					this.formData.temperature = res.data.temperature
					this.formData.humidity = res.data.humidity
				}
			} catch (error) {
				console.error('获取温湿度数据失败:', error)
				uni.showToast({
					title: '获取温湿度数据失败，请手动输入',
					icon: 'none'
				})
			}
		},
		
		// 获取单位名称
		async getUnitName(unitId) {
			if (!unitId) return ''
			try {
				const unitInfo = await getUnitApi(unitId)
				return unitInfo?.data?.name || ''
			} catch (error) {
				console.error('获取单位信息失败:', error)
				return ''
			}
		},
		
		// 格式化日期时间
		formatDateTime(input) {
			if (!input) return ''

			let date
			if (input instanceof Date) {
				date = input
			} else {
				date = new Date(input)
			}

			if (isNaN(date.getTime())) return ''

			// 返回 uni-datetime-picker 需要的格式
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const hours = String(date.getHours()).padStart(2, '0')
			const minutes = String(date.getMinutes()).padStart(2, '0')
			const seconds = String(date.getSeconds()).padStart(2, '0')

			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
		},
		
		// 格式化日期
		formatDate(date, format) {
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const hours = String(date.getHours()).padStart(2, '0')
			const minutes = String(date.getMinutes()).padStart(2, '0')
			
			return format
				.replace('yyyy', year)
				.replace('MM', month)
				.replace('dd', day)
				.replace('HH', hours)
				.replace('mm', minutes)
		},
		

		
		// 确保表单数据有效
		ensureValidFormData() {
			// 确保数值字段有效
			if (this.formData.costHeadcount === null || this.formData.costHeadcount === undefined) {
				this.formData.costHeadcount = ''
			}
			if (this.formData.quantity === null || this.formData.quantity === undefined) {
				this.formData.quantity = ''
			}
			if (this.formData.piece === null || this.formData.piece === undefined) {
				this.formData.piece = ''
			}
			if (this.formData.temperature === null || this.formData.temperature === undefined) {
				this.formData.temperature = ''
			}
			if (this.formData.humidity === null || this.formData.humidity === undefined) {
				this.formData.humidity = ''
			}
			
			// 确保字符串字段有效
			if (this.formData.workNo === null || this.formData.workNo === undefined) {
				this.formData.workNo = ''
			}
			if (this.formData.type === null || this.formData.type === undefined) {
				this.formData.type = ''
			}
			if (this.formData.reportCode === null || this.formData.reportCode === undefined) {
				this.formData.reportCode = ''
			}
			if (this.formData.line === null || this.formData.line === undefined) {
				this.formData.line = ''
			}
			if (this.formData.batchNo === null || this.formData.batchNo === undefined) {
				this.formData.batchNo = ''
			}
			if (this.formData.remark === null || this.formData.remark === undefined) {
				this.formData.remark = ''
			}
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 提交表单
		async submitForm() {
			// 表单验证
			if (!this.validateForm()) {
				return
			}
			
			this.formLoading = true
			
			try {
				// 准备提交数据
				const submitData = {
					id: this.formData.id || 0,
					workId: this.formData.workId,
					workNo: this.formData.workNo || '',
					type: this.formData.type || '',
					reportCode: this.formData.reportCode || '',
					startTime: this.formData.startTime,
					endTime: this.formData.endTime,
					costTime: this.formData.costTime.toString(),
					costHeadcount: this.formData.costHeadcount ? Number(this.formData.costHeadcount) : 0,
					line: this.formData.line || '',
					quantity: this.formData.quantity ? Number(this.formData.quantity) : 0,
					piece: this.formData.piece ? Number(this.formData.piece) : 0,
					batchNo: this.formData.batchNo || '',
					temperature: this.formData.temperature ? Number(this.formData.temperature) : 0,
					humidity: this.formData.humidity ? Number(this.formData.humidity) : 0,
					remark: this.formData.remark || '',
					slotNo: 0
				}
				
				// 提交数据
				if (this.formData.id) {
					// 修改
					await updateReportOrderApi(submitData)
					uni.showToast({
						title: '修改成功',
						icon: 'success'
					})
				} else {
					// 新增
					await createReportOrderApi(submitData)
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
				}
				
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
				
			} catch (error) {
				console.error('保存失败:', error)
				uni.showToast({
					title: '保存失败',
					icon: 'error'
				})
			} finally {
				this.formLoading = false
			}
		},
		
		// 表单验证
		validateForm() {
			if (!this.formData.type) {
				uni.showToast({
					title: '请选择任务类型',
					icon: 'none'
				})
				return false
			}
			
			if (!this.formData.startTime) {
				uni.showToast({
					title: '请选择开始时间',
					icon: 'none'
				})
				return false
			}
			
			if (!this.formData.endTime) {
				uni.showToast({
					title: '请选择结束时间',
					icon: 'none'
				})
				return false
			}
			
			if (this.formData.endTime <= this.formData.startTime) {
				uni.showToast({
					title: '结束时间不能早于开始时间',
					icon: 'none'
				})
				return false
			}
			
			if (!this.formData.quantity) {
				uni.showToast({
					title: '请输入数量',
					icon: 'none'
				})
				return false
			}
			
			return true
		}
	}
}
</script>

<style scoped>
.report-edit-page {
	height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f8f9fa;
}

/* 表单容器 */
.form-container {
	flex: 1;
	padding-bottom: 80px; /* 为底部按钮留出空间 */
}

/* 表单卡片 */
.form-card {
	background-color: #fff;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
	border: 1px solid #f0f0f0;
}

/* 表单项 */
.form-item {
	margin-bottom: 20px;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	display: block;
	font-size: 15px;
	font-weight: 500;
	color: #333;
	margin-bottom: 10px;
	position: relative;
}

.form-label.required::after {
	content: '*';
	color: #ff4757;
	margin-left: 4px;
	font-size: 16px;
}

.form-input {
	width: 100%;
	height: 44px;
	padding: 0 14px;
	border: 1px solid #e1e5e9;
	border-radius: 8px;
	font-size: 15px;
	background-color: #fff;
	box-sizing: border-box;
	transition: border-color 0.2s ease;
}

.form-input:focus {
	border-color: #007AFF;
	outline: none;
}

.form-input.disabled {
	background-color: #f8f9fa;
	color: #6c757d;
	border-color: #e9ecef;
}

.form-textarea {
	width: 100%;
	min-height: 88px;
	padding: 12px 14px;
	border: 1px solid #e1e5e9;
	border-radius: 8px;
	font-size: 15px;
	background-color: #fff;
	box-sizing: border-box;
	resize: none;
	line-height: 1.5;
	transition: border-color 0.2s ease;
}

.form-textarea:focus {
	border-color: #007AFF;
	outline: none;
}



/* 用时输入组 */
.duration-input-group {
	display: flex;
	align-items: center;
	gap: 12px;
	flex-wrap: wrap;
}

.duration-input {
	width: 80px;
	height: 44px;
	padding: 0 10px;
	border: 1px solid #e1e5e9;
	border-radius: 8px;
	font-size: 15px;
	text-align: center;
	background-color: #fff;
	transition: border-color 0.2s ease;
}

.duration-input:focus {
	border-color: #007AFF;
	outline: none;
}

.duration-label {
	font-size: 15px;
	color: #6c757d;
	font-weight: 500;
}

/* 数量输入组 */
.quantity-input-group {
	display: flex;
	align-items: center;
	gap: 12px;
}

.unit-text {
	font-size: 15px;
	color: #6c757d;
	font-weight: 500;
	min-width: 40px;
}

/* 底部固定按钮 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	gap: 12px;
	padding: 16px;
	background-color: #fff;
	border-top: 1px solid #f0f0f0;
	box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
}

.btn-cancel {
	flex: 1;
	height: 48px;
	border: 1px solid #e1e5e9;
	border-radius: 12px;
	background-color: #fff;
	color: #6c757d;
	font-size: 16px;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.btn-cancel:active {
	background-color: #f8f9fa;
	transform: scale(0.98);
}

.btn-submit {
	flex: 1;
	height: 48px;
	border: none;
	border-radius: 12px;
	background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
	color: #fff;
	font-size: 16px;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
	box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.btn-submit:active {
	transform: scale(0.98);
	box-shadow: 0 2px 8px rgba(0, 122, 255, 0.4);
}

.btn-submit:disabled {
	background: #e9ecef;
	color: #6c757d;
	box-shadow: none;
	transform: none;
}

/* 加载遮罩 */
.loading-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	backdrop-filter: blur(4px);
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12px;
	background-color: #fff;
	padding: 24px;
	border-radius: 16px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.loading-text {
	font-size: 15px;
	color: #6c757d;
	font-weight: 500;
}

/* 修复 uni-datetime-picker 时分秒选择弹出层样式问题 */
</style>

<style>
/* 全局样式修复 uni-datetime-picker 时分秒弹出层问题 */
.uni-datetime-picker-mask {
	position: fixed !important;
	bottom: 0px !important;
	top: 0px !important;
	left: 0px !important;
	right: 0px !important;
	background-color: rgba(0, 0, 0, 0.4) !important;
	transition-duration: 0.3s !important;
	z-index: 9998 !important;
}

.uni-datetime-picker-popup {
	border-radius: 8px !important;
	padding: 30px !important;
	/* width: 270px !important; */
	background-color: #fff !important;
	position: fixed !important;
	top: 50% !important;
	left: 50% !important;
	transform: translate(-50%, -50%) !important;
	transition-duration: 0.3s !important;
	z-index: 9999 !important;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

/* 修复时分秒选择器内部样式 */
.uni-datetime-picker-view {
	height: 130px !important;
	width: 270px !important;
}

.uni-datetime-picker-item {
	height: 50px !important;
	line-height: 50px !important;
	text-align: center !important;
	font-size: 14px !important;
	color: #333 !important;
}

.uni-datetime-picker-btn {
	margin-top: 60px !important;
	display: flex !important;
	flex-direction: row !important;
	justify-content: space-between !important;
}

.uni-datetime-picker-btn-text {
	font-size: 14px !important;
	color: #007AFF !important;
	padding: 8px 16px !important;
	cursor: pointer !important;
}

.uni-datetime-picker-btn-group {
	display: flex !important;
	flex-direction: row !important;
}

.uni-datetime-picker-cancel {
	margin-right: 30px !important;
}

/* 修复 picker-view 在弹出层中的显示问题 */
.uni-datetime-picker-popup picker-view {
	width: 100% !important;
	height: 130px !important;
}

.uni-datetime-picker-popup picker-view-column {
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
}

/* 确保弹出层在最顶层显示 */
.uni-datetime-picker-popup,
.uni-datetime-picker-mask {
	z-index: 10000 !important;
}

/* 修复日期和时分秒输入框对齐问题 */
.popup-x-header {
	display: flex !important;
	flex-direction: row !important;
	align-items: center !important;
	gap: 10px !important;
	padding: 10px 0 !important;
}

.popup-x-header .uni-date__input {
	flex: 1 !important;
	height: 40px !important;
	line-height: 40px !important;
	text-align: center !important;
	border: 1px solid #e1e5e9 !important;
	border-radius: 4px !important;
	background-color: #fff !important;
	font-size: 14px !important;
}

.popup-x-header .uni-date__input:focus {
	border-color: #007AFF !important;
	outline: none !important;
}

/* 修复时间选择器在弹出层中的样式 */
.popup-x-header .uni-datetime-picker {
	flex: 1 !important;
}

.popup-x-header .uni-datetime-picker-timebox-pointer {
	height: 40px !important;
	line-height: 40px !important;
	border: 1px solid #e1e5e9 !important;
	border-radius: 4px !important;
	background-color: #fff !important;
	text-align: center !important;
	font-size: 14px !important;
}

/* 修复范围选择时的对齐问题 */
.popup-x-header--datetime {
	display: flex !important;
	flex-direction: row !important;
	align-items: center !important;
	flex: 1 !important;
	gap: 8px !important;
}

.uni-date-range__input {
	text-align: center !important;
	max-width: none !important;
	flex: 1 !important;
}

/* 修复移动端适配问题 */
@media screen and (max-width: 768px) {
	.uni-datetime-picker-popup {
		width: 90vw !important;
		max-width: 320px !important;
		padding: 20px !important;
	}

	.uni-datetime-picker-view {
		width: 100% !important;
	}

	.popup-x-header {
		flex-direction: column !important;
		gap: 8px !important;
	}

	.popup-x-header--datetime {
		flex-direction: column !important;
		gap: 8px !important;
	}
}
</style>
